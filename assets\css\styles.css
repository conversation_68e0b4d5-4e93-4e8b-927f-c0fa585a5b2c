  html, body {overflow-x: hidden;width: 100%;}

  /* Header Styles */
  .logo-img{width: 60px;padding-top: 10px;padding-bottom: 7px;}
  .head {position: relative;z-index: 4;transition: all 0.3s ease;}
/* BLACK navbar - at top of page */
  .head.navbar-black,
  header.head.navbar-black {box-shadow: none !important;}
  /* BLUE navbar - when scrolling */
  .head.navbar-blue,
  header.head.navbar-blue {background: #182561 !important;background-color: #182561 !important;box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;}
  .phone-no {color: white;font-size: 17px;display: flex;align-items: center;gap: 8px;}
  .btn.nav-demo {font-size: 17px;min-width: 120px;padding: 12px 27px;border-radius: 5px;}
  .nav-item a {font-size: 17px;}

  /* Desktop dropdown styles */
  .nav-item.dropdown .dropdown-menu {background: #fff;border: none;border-radius: 8px;}
  .nav-item.dropdown .dropdown-item {padding: 12px 20px;font-size: 15px;}
  .nav-item.dropdown .dropdown-item:hover {background: #333;color: #fff;}
  .dropdown-toggle::after {vertical-align:baseline !important}

  /* Hamburger Styles */
  .menu-toggle {display: flex;flex-direction: column;background: transparent;border: none;width: 36px;height: 34px;padding: 4px;align-items: flex-start;justify-content: center;z-index: 105;}
  .menu-toggle .menu-bar {display: block;width: 24px;height: 3px;background:white;margin: 3px 0;border-radius: 2px;}
  
  /* Hamburger open animation-start */
  .menu-open .menu-toggle .menu-bar:nth-child(1) {transform: rotate(45deg) translate(5px, 5px);}
  .menu-open .menu-toggle .menu-bar:nth-child(2) {opacity: 0;}
  .menu-open .menu-toggle .menu-bar:nth-child(3) {transform: rotate(-45deg) translate(6px, -6px);}
  /* Hamburger open animation-end */

  /* Mobile Nav-start */
  .mobile-nav {position: fixed;top: 0;right: 0;height: 100vh;width: 100%;max-width: 400px;background: #ffffff;z-index: 110;transform: translateX(100%);transition: transform 0.3s ease;box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1); overflow-y: auto;}
  .mobile-menu-header {  display: flex;  align-items: center;justify-content: space-between;padding: 40px 30px 30px;background: #ffffff;border-bottom: 1px solid #f0f0f0;}
  .mobile-logo-container {display: flex;align-items: center;}
  .mobile-logo-img {width: 60px;height: auto;}
  .mobile-close-btn {background: #fff5f5;border: 1px solid #fecaca;width: 36px;height: 36px;border-radius: 50%;display: flex;align-items: center;justify-content: center;cursor: pointer;}
  .mobile-close-btn i {color: #ef4444;font-size: 14px;}
  .mobile-close-btn:hover {background: #fef2f2;border-color: #f87171;}
  .mobile-menu-content {padding: 30px 0 40px;flex: 1;display: flex;flex-direction: column;}
  .mobile-nav-list {list-style: none;padding: 0;margin: 0;flex: 1;}
  .mobile-nav-item {margin-bottom: 0;}
  .mobile-nav-link {display: flex;align-items: center;justify-content: space-between;padding: 20px 30px;color: #6b7280;text-decoration: none;font-size: 18px;font-weight: 400;}
  .mobile-nav-link:hover {color: #374151;background: #f9fafb;text-decoration: none}
  .nav-text {flex: 1;text-align: left;}
  .mobile-dropdown-icon {width: 20px;height: 20px;display: flex;  align-items: center;justify-content: center;background: transparent;border-radius: 2px;}
  .mobile-dropdown-icon i {font-size: 14px;color: #9ca3af;}
  .mobile-dropdown.active .mobile-dropdown-icon i {color: #6b7280;transform: rotate(45deg);}
  .mobile-dropdown-menu {list-style: none;padding: 0;margin: 0;background: #ffffff;display: none;border-top: 1px solid #e5e7eb;}
  .mobile-dropdown.active .mobile-dropdown-menu {display: block;}
  .mobile-dropdown-item {display: block;padding: 15px 50px;color: #6b7280;text-decoration: none;font-size: 16px;font-weight: 400;transition: all 0.3s ease;border-bottom: 1px solid #f3f4f6;}
  .mobile-dropdown-item:hover {color: #3b82f6;background: #f8faff;text-decoration: none;}
  .mobile-action-buttons {padding: 0 30px 40px;display: flex;flex-direction: row;gap: 12px;margin-top: auto;}
  .mobile-btn {padding: 12px 24px;border-radius: 8px;font-size: 16px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;text-align: center;flex: 1;border: 1px solid transparent;}
  .mobile-btn-outline {background: transparent;color: #3b82f6;border: 1px solid #e5e7eb;}
  .mobile-btn-outline:hover {background: #f8faff;border-color: #3b82f6;}
  .mobile-btn-primary {background: #3b82f6;color: #ffffff;border: 1px solid #3b82f6;}
  .mobile-btn-primary:hover {background: #2563eb;border-color: #2563eb;}
  /* Mobile Nav-end */

  /* Menu Open State-start */
  .menu-open .mobile-nav {transform: translateX(0);}
  .menu-open .mobile-menu-overlay {display: block;opacity: 1;}
  .menu-open body {overflow: hidden;}
  .close-btn{background: rgba(255, 107, 53, .12);width: 30px;height: 30px;text-align: center;outline: none;-webkit-box-shadow: none;box-shadow: none;border: none;border-radius: 20px;}
  /* Menu Open State-end*/

  /* Hero Section-start */
  .smart {position: relative;background-color: #171717;align-items: center;}
  .smart-header{margin-left: -35px;}
  .smart::before {content: '';position: absolute;top: 0;left: 0;width: 100%;height: 100%;background: url("../images/ca-hero-bg.png") no-repeat center;z-index: 0;}
  .smart::after {content: '';position: absolute;top: -90px;left: 440px;width: 109%;height: 95%;background: url("../images/heading-shape.png") no-repeat center;z-index: 1;}
  .smart h1,
  .smart-text {position: relative;z-index: 1;color: white;}
  .smart h1 {font-style: normal;font-size: 72px; font-weight:700;padding-top: 190px;padding-bottom: 40px;padding-left: 30px;}
  .smart-text {padding-top: 80px;padding-left: 25px;font-size: 17px;line-height: 1.6;}
  .smart-text-right {padding-top: 80px;padding-right: 25px;font-size: 17px;line-height: 1.6;text-align: right;}
  .smart-content-right {display: flex;align-items: center;height: 100%;}
  .highlight {color: #f38b32;}
  /* Hero Section-end */

  /* software-section-start */
  .software{background-color: #f4f6ff;}
  .software-heading {font-size: 48px;font-weight: 700;color: #333;padding: 60px 0px;text-align: center;padding-top: 250px;}
  .flex-container {display: flex;flex-wrap: wrap;justify-content: center;gap: 40px;}
  .software-text {display: flex;align-items: center;font-size: 22px;font-weight: 700;color: #666;text-align: left;}
  .software-text i {background-color: #eaeaea;color: white;width: 45px;height: 45px;border-radius: 50%;display: flex;align-items: center;justify-content: center;margin-right: 15px;font-size: 18px;}
  .nav-link.active .software-text i {
  background-color: #ff6b35;
}

  .nav-tabs{justify-content: center;gap: 14px;padding: 18px 24px;border: none !important;}
  .nav-link{padding: 20px !important;}
  .tab-content {margin-top: 60px;border: 1px solid #ffb86c;border-radius: 16px;margin-bottom: 30px;position: relative;}
  .health-heading{padding-left: 36px;}
  .health-heading h1{font-size: 35px;font-weight: 700;font-style: normal;text-align: left;}
  .health-heading p{font-size: 18px;text-align: start;color: #666;}
  .health-text{font-size: 18px;color: #666;text-align: start;}
  .health-overview{padding:28px 75px;background-color: white;border-radius: 16px;margin: 18px;}
  .health-img{border-radius: 16px;}
  /* software-section-end */

  /* feature-section-start */
  .feature{background-color:#171717;margin-bottom: -545px;margin-top: -225px;}
  .feature-heading{font-size: 48px;font-weight: 700;color:white;margin-bottom: 50px;margin-top: 310px; }
  .feature-para{max-width: 70ch;color: #d7d7d7;font-size: 18px;line-height: 35px;font-weight: 500;}
  .feature-list{font-size: 17px;list-style: none;}
  .shape-img{margin-top: 305px;}
  .feature-adv{margin-top: 30px;}
  .feature-adv h3{color: white;}
  .feature-img{padding: 19px;border: 1px solid #ffb86c;border-radius: 16px; margin-top: 30px;}
  /* feature-section-end */

  /* Technologies-section-start */
  .Technologies{background-color: white;padding-bottom: 180px;}
  .tech-h5{position: relative;padding-top: 185px;font-size: 35px;font-style: normal;font-weight: 700;line-height: 70px;}
  .tech-h5::after {content: "";position: absolute;top: 87%;left: 57%;width: 96px;height:4px;background: linear-gradient(287.85deg, #f38b32 0%, #FF7E5D 95.32%);}
  .tech-h1{font-size: 48px;font-style: normal;font-weight: 700;}
  .tech-img{padding-top: 40px;}
  /* Technologies-section-end */

  /* Ready Software-start */
  .Ready-Software{background-image: url(../images/ca-service-section-bg.png);background-size: cover;background-position: center;background-repeat: no-repeat;padding-top: 120px;padding-bottom: 120px;}
  .ready-h1{width: 97%;font-size: 48px;font-weight: 700;font-style: normal;}
  .text-stroke {-webkit-text-fill-color: rgba(0, 0, 0, 0);-webkit-text-stroke: 1px #f48a37;font-size: 65px;padding-top: 25px;}
  .heading-6{font-size: 20px;font-weight: 700;color: black;}
  .digital-service-card{background-color: rgba(255, 255, 255, .5);border-radius: 16px;border: 1px solid rgba(239, 62, 62, .15);padding-bottom:20px;margin-top:75px;}
  .clr-text{font-size: 17px;color: black;font-weight: 400;padding-top: 6px;padding-left: 26px;padding-right: 36px;}
  /* Ready Software-end  */

  /* testimonial-section-start */
  .about{padding: 120px 0px;}
  .heading-3{font-size: 48px;font-weight: 700;line-height: 60px;}
  .about-para{color: #666;font-size:20px;}
  .testimonial{padding-top: 60px;}
  .quote-img{width: 55px;margin-left: 1px;}
  .testimonial-h6{width: 581px;margin-left: -190px;margin-top: 65px;font-size: 21px;color: black;}
  .about-passage{position: relative;margin-top: 219px;margin-left: -293px;}
  .about-passage::after{position: absolute;content: " ";top: 16%;left: -18%;width: 2.5rem;height:4px;background: linear-gradient(287.85deg, #f38b32 0%, #FF7E5D 95.32%);}
  .about-passage h6{font-size: 22px;margin-left: -40px;}
  .about-passage p{font-size: 18px;color: #666;margin-left: -40px;}
  .testimonial-1-h6{width: 581px;margin-left: 30px;margin-top: 65px;font-size: 21px;color: black;}
  /* testimonial-section-end */

  /* Frequently-setion-start */
  .Frequently{background-color: #171717;padding: 120px 100px;}
  .freq-overall{margin-top: 70px;}
  .frequently-h1 {color: white;font-size: 48px;font-weight: 700;font-style: normal; }
  .faq-header{background-color: #393b3f;color: white;font-weight: 600;border-radius: 6px;padding: 6px 30px; }
  .question{margin-top: 80px; }
  .faq-header-1 {background-color: #f55f4e;color: white;font-weight: 600;border-radius: 6px;padding: 6px 30px;}
  .tab-content{margin-top: 40px;border: 1px solid #ffb86c;border-radius: 16px;}
  .accordion{margin-top: 40px;}
  .accordion-item {padding: 8px 40px;background: #1c1c1c !important;margin-bottom: 20px;border-radius: 5px !important;cursor: pointer;color: white;font-size: 20px;font-weight: 600;border: 1px solid #2E2E2E !important; }
  .accordion-item:hover {background: #fff !important;color:black !important;}
  .accordion-body{font-size: 20px;line-height: 35px;color: white;margin-top: 40px;}
  .accordion-button::after {display: none;}
  .accordion-button {font-weight: 500;box-shadow: none !important;background: none !important;color: white !important;font-size: 20px !important;border: none !important;}
  .accordion-item:hover .accordion-button{color: black !important;}
  .accordion-para{border: 1px solid #ffb86c;border-radius: 16px;padding: 45px;}
  /* Frequently-setion-end */

  /* owl carousel-start */
  .owl-dots button.owl-dot.active{background-color: #f38b32 !important;}
  .owl-dots button.owl-dot{position: relative;background-color: #d9d9d9 !important;height: 8px;width: 8px;border-radius: 20px;margin: 0 5px;top: 30px;left: 340px;}
  /* owl carousel-end */

  /* connect-section-start */
  .connect{padding-top: 60px;background: url(../images/contact-us-bg.svg) no-repeat center bottom;padding-bottom: 120px;}
  .connect-heading{font-size: 40px;font-weight: 700;font-style: normal;}
  .connect-para{font-size: 18px;color:#666;line-height: 40px;}
  .register-form{margin-top:40px;}
  .contact-img{margin-top: 40px;max-width: 100%;}
  .input-group .form-control{ padding: 13px;}
  /* connect-section-end */

  /* footer-section-start */
  .footer{background: url(../images/page-header-bg.svg) no-repeat bottom right;background-color: #111827 !important;}
  .logo-white{width: 61px;padding-top: 25px;}
  .footer-overall{padding: 120px 0px;}
  .footer-para{ color: #d9d9d9;;font-size: 18px;padding-top: 20px;}
  .footer-heading{color: white;font-size: 20px;font-weight: 700;}
  .footer-nav-list{list-style: none;padding-top: 30px;  }
  .text-decoration-none{color: #d9d9d9;;font-size: 16px;line-height: 35px;}
  .contact-info {list-style: none;padding-top: 30px;line-height: 35px;}
  .contact-info li { color: #d9d9d9;; margin-bottom: 10px; align-items: center;}
  .contact-info i {text-decoration:none;margin-right: 10px;color: #d9d9d9;;}
  .contact-info a {color: #d9d9d9;;text-decoration: none; }
  .social-icons {display: flex;gap: 10px;margin-left: 30px;}
  .social-icons a {display: flex;justify-content: center;align-items: center;width: 35px;height: 35px;border: 1px solid #555;border-radius: 5px;color: #fff;text-decoration: none;}
  .social-icons a:hover { background: #d9d9d9;; color: #000;}
  /* footer-section-end */

  /* footer-bottom-section-start */
  .footer-bottom{background-color: #111827;border-top: 1px solid rgba(248, 249, 250, .05);}
  .copyright-text p{margin:20px;color: #d9d9d9;; font-size: 14px;}
  /* footer-bottom-section-end */


    /* MOBILE (max-width: 767px) */
  @media (max-width: 767px) {
  .mobile-logo-img {width: 40px;height: auto;}
  .mobile-close-btn {width: 30px;height: 30px;}
  .mobile-btn {padding: 12px 24px;border-radius: 8px;font-size: 16px;font-weight: 500;}
  .mobile-btn-outline {color: #3b82f6;border: 2px solid #3b82f6;}
  .head.navbar-black, header.head.navbar-black {background: #182561 !important;background-color: #182561!important;box-shadow: none !important;}
  .mobile-nav {left: 1px;}
  .mobile-nav-link { padding: 10px 30px;}
  .mobile-menu-header {border: none;}
  .mobile-menu-content {padding:0px;}
  .menu-toggle {margin-right: 0px;}
  .nav-tabs {justify-content:left;gap: 8px;padding: 10px 15px;}
  .nav-link{padding: 15px 10px !important;}

  /* Hero Section */
  .smart h1 {font-style: normal;font-size: 47px;line-height: 55px;padding-left: 40px;padding-top: 25px;font-weight: 700;}
  .smart-img {padding: 10px;border: 1px solid #ffb86c;border-radius: 16px;}
  .smart-text {padding-top: 180px;font-size: 17px;line-height: 30px;padding-left: 0px;}
  .smart {position: relative;background-color: black;padding-top: 140px;padding-bottom: 50px;align-items: center;}
  .smart::before {content: '';position: absolute;top: 0;left: 0;width: 100%;height: 100%;background: url(../images/ca-hero-bg.png) no-repeat center;z-index: 0;}

    /* software Section-start */
  .software-heading {font-size: 32px;font-weight: 700;color: #333;text-align: center;margin-top: -220px;}
  .software-text {font-size: 18px;}
  .flex-container {display: flex;flex-wrap: wrap;gap: 8px;justify-content:left;padding-left: 15px;margin-top: -20px;}

 /* health-section - Remove gaps and align properly */
  .tab-content {margin-top: 20px;border: 1px solid #ffb86c;border-radius: 16px;margin-bottom: 20px;margin-left: 0;margin-right: 0;}
  .health-overview{padding: 15px;background-color: white;border-radius: 16px;margin: 0;}
  .health-section {border-radius: 16px;}
  .health-heading {padding-left: 0px;}
  .health-heading h1 {font-size: 24px;font-weight: 700;font-style: normal;}
  .health-text {font-size: 18px;color: #666;text-align: start;padding-bottom: 20px;}
  .health-img img{border-radius: 10px;}
  .health-heading p{padding-top: 15px;padding-bottom: 15px;}

/* feature-section */
  .feature {margin-bottom: -495px;margin-top: -400px;}
  .feature-heading {font-size: 32px;margin-bottom: 50px;margin-top: 425px;}
  .shape-img {margin-top: 0px;margin-left: 0px;display: none;}
  .feature {background-color: black;padding-bottom: 570px;}

    /* Technologies */
  .Technologies {padding-bottom: 60px;}
  .tech-h5 { position: relative;padding-top: 60px;font-size: 30px;font-style: normal;line-height: 50px;}
  .tech-h5::after {content: "";position: absolute;top: 81%;left: 86%;width: 58px;height: 4px; background: linear-gradient(287.85deg, #f38b32 0%, #FF7E5D 95.32%);}
  .tech-h1 {font-size: 31px;font-style: normal;font-weight: 700;line-height: 75px;}
  .tech-img {padding-top: 0px;}

/* ready-software-section */
  .Ready-Software {padding-bottom: 60px; padding-top: 60px;}
  .ready-h1 {padding-top:0px;width: 98%;font-size: 32px;}
  .digital-cards{margin-top: 50px;}
  .digital-service-card {margin-top: 0px;}

/* testimonial-section */
  .quote-img {margin-top: 50px;width: 70px;}
  .testimonial {padding-top: 25px;}
  .testimonial-cards-section {padding: 0px;}
  .testimonial-h6 {font-size: 23px;margin-left: -380px;margin-top: 125px;}
  .about {padding-bottom:95px; padding-top:60px;}
  .about-para {font-size: 21px;line-height: 35px;}
  .heading-3 {font-size: 32px;font-weight: 700;line-height: 40px;}
  .about-passage {margin-top: 340px;margin-left: -608px;}
  .about-passage h6 {font-size: 28px;font-weight: 700;margin-left: -75px;}
  .about-passage::after {position: absolute;content: " ";top: 20%;left: -15%;width: 3.5rem;height: 5px;}
  .about-passage p {font-size: 24px;margin-left: -75px;}
  .owl-dots button.owl-dot {position: relative;height: 8px;width: 10px;top: 50px;left: 145px;}
  .owl-nav{ display: none;}
  .owl-dots button:nth-child(n+4), 
  .swiper-pagination-bullet:nth-child(n+4) {display: none !important;}

  /* frequently-dection */
  .Frequently {background-color: #171717;padding: 60px 20px;margin-top: 20px;}
  .frequently-h1 {font-size: 32px;margin-bottom: 50px;}
  .faq-header {padding: 6px 30px;margin-left: -20px;}
  .faq-header-1 {margin-left: -20px;}
  .faq-item.active {font-size: 20px;font-weight: 600;}
  .faq-list-group {margin-top: 30px;margin-bottom: 50px;}
  .faq-item {padding: 18px 21px;}
  .question {margin-top: 40px;margin-left: 0;margin-right: 0;}
  .tab-content {margin-top: 20px;}
  .tab-para {padding: 15px;}
  .accordion-para {padding: 15px;width: 100%;margin-left: 0;}
  .accordion-flush{margin-bottom: 55px;}
  .accordion-item {padding: 13px 15px;width: 100%;margin-left: 0;}
  .accordion-button {font-size: 22px !important;}
  
/* connect-section */
  .connect {padding-top: 30px;padding-bottom: 60px;}
  .connect-heading {font-size: 26px;font-weight: 700;padding-top: 30px;}
  .connect-para {line-height: 30px;font-size: 17px;}

  /* footer-section */
  .footer-overall {padding: 50px 0px;}
  .logo-white {width: 45px;padding-top: 12px;margin-left: 25px;}
  .social-icons {display: flex;gap: 10px;margin-left: 0px;}
  .footer-info {padding-top: 25px;}
  .footer-nav-list {padding-left: 0px;}
  .contact-info {padding-left: 0px;}
  .footer-heading {margin-top: 20px;}
  .copyright-text p { margin-top: 30px; margin-bottom: 40px;margin-left: 0px;}
  .footer-overall {padding: 60px 0px;}
  .contact-info i {text-decoration: none;margin-right: 1px;color: #fff;}
  }

  /* Hide desktop menu on small screens */
  @media (max-width: 991px) {
  .d-lg-none { display: block !important; }
  .d-lg-flex, .d-none.d-lg-flex { display: none !important; }

  /* Mobile menu responsive adjustments */
  .mobile-nav {max-width: 380px;}
}
  /* Medium screens */
  @media (max-width: 768px) {
  .mobile-nav {max-width: 100%;width: 100%;}
  .mobile-action-buttons {flex-direction:row;gap: 15px;}
  .mobile-btn {width: 100%;}

  /* Remove gaps and align content for mobile */
  .software {padding-left: 15px;padding-right: 15px;}
  .nav-tabs {justify-content: flex-start;gap: 5px;padding: 8px 0;margin-left: 0;margin-right: 0;}
  .nav-link {padding: 12px 8px !important;font-size: 14px;}
  .software-text {font-size: 16px;}
  .software-text i {width: 35px;height: 35px;margin-right: 10px;font-size: 14px;}

  .tab-content {margin-top: 15px;margin-bottom: 15px;margin-left: 0;margin-right: 0;border-radius: 12px;}
  .health-overview {padding: 12px;margin: 0;border-radius: 12px;}
  .health-heading {padding-left: 0;padding-right: 0;}
  .health-heading h1 {font-size: 22px;margin-bottom: 10px;}
  .health-heading p {font-size: 16px;margin-bottom: 15px;}
  .health-text {font-size: 16px;padding-bottom: 15px;}

  /* Ensure full width alignment */
  .container {padding-left: 15px;padding-right: 15px;}
  .row {margin-left: 0;margin-right: 0;}
  .col-lg-5, .col-lg-6 {padding-left: 8px;padding-right: 8px;}
  }


